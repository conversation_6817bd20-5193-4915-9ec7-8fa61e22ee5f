<template>
  <div class="change-custom-component">
    <!-- 画笔类型 -->
    <div class="title">画笔种类</div>
    <div class="brush-types">
      <div
        class="brush-type"
        :class="{ active: currentStore.brushType === 'keep' }"
        @click="setBrushType('keep')"
      >
        <img
          :src="`/img/pic/second2/${
            currentStore.brushType  === 'keep' ? 'icon_bl_xz' : 'icon_bl_wxz'
          }@2x.png`"
          alt=""
        />
      </div>
      <div
        class="brush-type"
        :class="{ active: currentStore.brushType === 'erase' }"
        @click="setBrushType('erase')"
      >
        <img
          :src="`/img/pic/second2/${
            currentStore.brushType  === 'erase' ? 'icon_cc_xz' : 'icon_cc_wxz'
          }@2x.png`"
          alt=""
        />
      </div>
    </div>

    <!-- 画笔设置 -->
    <div v-show="!currentStore.isOrigin" class="module">
      <div class="title">画笔设置</div>
      <div class="brush-setting">
        <div class="desc">画笔大小</div>
        <el-slider
          v-model="brushSize"
          :min="1"
          :max="100"
          :format-tooltip="(value: any) => `${value}px`"
          @change="updateBrushSize"
        />
        <span class="value">{{ brushSize }}px</span>
      </div>
      <div class="brush-setting" style="margin-bottom: 12px">
        <div class="desc">边缘柔和</div>
        <el-slider
          v-model="edgeSoftness"
          :min="0"
          :max="100"
          :format-tooltip="(value: any) => `${value}%`"
          @change="updateEdgeSoftness"
        />
        <span class="value">{{ edgeSoftness }}%</span>
      </div>
    </div>

    <!-- 操作 -->
    <div class="module">
      <div class="title">操作</div>
      <div class="operations">
        <div v-show="!currentStore.isOrigin" class="operation-row">
          <div
            class="operation-btn btn"
            :class="{
              gray: currentStore.maskHistoryIndex === 0,
            }"
            @click="undo"
          >
            <i class="operation-icon undo"></i>
            <span>撤销</span>
          </div>
          <div
            class="operation-btn btn"
            :class="{
              gray:
                currentStore.maskHistoryIndex ===
                currentStore.maskHistory.length - 1,
            }"
            @click="redo"
          >
            <i class="operation-icon redo"></i>
            <span>恢复</span>
          </div>
          <div class="operation-btn btn" @click="reset">
            <i class="operation-icon reset"></i>
            <span>重置</span>
          </div>
          <div
            class="operation-btn btn"
            :class="{
              gray: currentStore.maskZoom <= 0.4,
            }"
            @click="zoomOut"
          >
            <i class="operation-icon zoom-out"></i>
            <span>缩小</span>
          </div>
        </div>
        <div v-show="!currentStore.isOrigin" class="operation-row">
          <div class="operation-btn btn" @click="zoomIn">
            <i class="operation-icon zoom-in"></i>
            <span>放大</span>
          </div>
          <div
            class="operation-btn btn"
            :class="{
              gray: currentStore.maskZoom === 1,
            }"
            @click="resetZoom"
          >
            <i class="operation-icon original-ratio"></i>
            <span>原比例</span>
          </div>
          <div class="operation-btn btn" @click="toggleDrag">
            <i
              class="operation-icon drag"
              :class="{
                'drag-selected': currentStore.maskDragMode,
              }"
            ></i>
            <span>拖动</span>
          </div>
        </div>
        <div v-show="currentStore.isOrigin" class="operation-row">
          <div
            class="operation-btn btn"
            :class="{
              gray: currentStore.maskZoom <= 0.4,
            }"
            @click="zoomOut"
          >
            <i class="operation-icon zoom-out"></i>
            <span>缩小</span>
          </div>
          <div class="operation-btn btn" @click="zoomIn">
            <i class="operation-icon zoom-in"></i>
            <span>放大</span>
          </div>
          <div
            class="operation-btn btn"
            :class="{
              gray: currentStore.maskZoom === 1,
            }"
            @click="resetZoom"
          >
            <i class="operation-icon original-ratio"></i>
            <span>原比例</span>
          </div>
          <div class="operation-btn btn" @click="toggleDrag">
            <i
              class="operation-icon drag"
              :class="{
                'drag-selected': currentStore.maskDragMode,
              }"
            ></i>
            <span>拖动</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { useBatchCropStore } from '@/pinia/batchCrop'
import { useMattingStore } from '@/pinia/matting'
import { useRoute } from 'vue-router'

const route = useRoute()
const cropStore = useBatchCropStore()
const mattingStore = useMattingStore()

// 获取当前应该使用的 store
const currentStore: any = computed(() => {
  switch (route.name) {
    case 'matting':
      return mattingStore
    default:
      return cropStore
  }
})

// 画笔类型：保留(keep)或擦除(erase)
const brushType = ref('keep')
// 画笔大小
const brushSize = ref(20)
// 边缘柔和度
const edgeSoftness = ref(0)
// 历史记录
const history = ref<string[]>([])
// 当前历史位置
const historyIndex = ref(-1)
// 是否处于拖动模式
const isDragging = ref(false)

// 设置画笔类型
const setBrushType = (type: string) => {
  currentStore.value.isOrigin = false
  nextTick(() => {
    console.log('[ChangeCustom] 设置画笔类型:', type)
    // 1、当前页面
    brushType.value = type
    // 2、通知父组件画笔类型变更
    if (currentStore.value.updateBrushType) {
      currentStore.value.updateBrushType(type)
      console.log('[ChangeCustom] 已通知 store 更新画笔类型:', type)
    } else {
      console.warn('[ChangeCustom] store 中未找到 updateBrushType 方法')
    }
    // 3、选中手动精修，需要去除拖拽模式
    if (type) {
      isDragging.value = false
      currentStore.value.toggleDragMode(false)
    }
  })
}

// 更新画笔大小
const updateBrushSize = (size: number) => {
  console.log('[ChangeCustom] 更新画笔大小:', size)
  // 通知父组件画笔大小变更
  if (currentStore.value.updateBrushSize) {
    currentStore.value.updateBrushSize(size)
    console.log('[ChangeCustom] 已通知 store 更新画笔大小:', size)
  } else {
    console.warn('[ChangeCustom] store 中未找到 updateBrushSize 方法')
  }
}

// 更新边缘柔和度
const updateEdgeSoftness = (softness: number) => {
  console.log('[ChangeCustom] 更新边缘柔和度:', softness)
  // 通知父组件边缘柔和度变更
  if (currentStore.value.updateEdgeSoftness) {
    currentStore.value.updateEdgeSoftness(softness)
    console.log('[ChangeCustom] 已通知 store 更新边缘柔和度:', softness)
  } else {
    console.warn('[ChangeCustom] store 中未找到 updateEdgeSoftness 方法')
  }
}

// 撤销操作
const undo = () => {
  console.log('[ChangeCustom] 执行撤销操作, 当前历史索引:', historyIndex.value)
  if (historyIndex.value > 0) {
    historyIndex.value--
    console.log('[ChangeCustom] 撤销到历史索引:', historyIndex.value)
    // 通知父组件撤销操作
    if (currentStore.value.applyMaskHistory) {
      currentStore.value.applyMaskHistory(historyIndex.value)
      console.log(
        '[ChangeCustom] 已通知 store 应用历史记录:',
        historyIndex.value
      )
    } else {
      console.warn('[ChangeCustom] store 中未找到 applyMaskHistory 方法')
    }
  } else {
    console.log('[ChangeCustom] 无法撤销，已经是最早的状态')
  }
}

// 重做操作
const redo = () => {
  console.log(
    '[ChangeCustom] 执行重做操作, 当前历史索引:',
    historyIndex.value,
    '历史长度:',
    history.value.length
  )
  if (historyIndex.value < history.value.length - 1) {
    historyIndex.value++
    console.log('[ChangeCustom] 重做到历史索引:', historyIndex.value)
    // 通知父组件重做操作
    if (currentStore.value.applyMaskHistory) {
      currentStore.value.applyMaskHistory(historyIndex.value)
      console.log(
        '[ChangeCustom] 已通知 store 应用历史记录:',
        historyIndex.value
      )
    } else {
      console.warn('[ChangeCustom] store 中未找到 applyMaskHistory 方法')
    }
  } else {
    console.log('[ChangeCustom] 无法重做，已经是最新的状态')
  }
}

// 重置操作
const reset = () => {
  console.log('[ChangeCustom] 执行重置操作')
  // 通知父组件重置操作
  if (currentStore.value.resetMask) {
    currentStore.value.resetMask()
    console.log('[ChangeCustom] 已通知 store 重置蒙版')
  } else {
    console.warn('[ChangeCustom] store 中未找到 resetMask 方法')
  }
  // 清空历史记录
  history.value = []
  historyIndex.value = -1
  console.log('[ChangeCustom] 已清空本地历史记录')
}

// 放大
const zoomIn = () => {
  console.log('[ChangeCustom] 执行放大操作')
  console.log('[ChangeCustom] 当前 store:', currentStore.value)
  // console.log('[ChangeCustom] store 可用方法:', Object.keys(currentStore.value || {}))

  // 通知父组件放大操作
  if (currentStore.value && typeof currentStore.value.zoomMask === 'function') {
    currentStore.value.zoomMask(0.2)
  } else {
    console.warn('[ChangeCustom] store 中未找到 zoomMask 方法')
    console.warn('[ChangeCustom] currentStore.value:', currentStore.value)
    console.warn(
      '[ChangeCustom] zoomMask 类型:',
      typeof currentStore.value?.zoomMask
    )
  }
}

// 缩小
const zoomOut = () => {
  console.log('[ChangeCustom] 执行缩小操作')
  console.log('[ChangeCustom] 当前 store:', currentStore.value)

  if (currentStore.maskZoom <= 0.4) return

  // 通知父组件缩小操作
  if (currentStore.value && typeof currentStore.value.zoomMask === 'function') {
    currentStore.value.zoomMask(-0.2)
  } else {
    console.warn('[ChangeCustom] store 中未找到 zoomMask 方法')
    console.warn('[ChangeCustom] currentStore.value:', currentStore.value)
    console.warn(
      '[ChangeCustom] zoomMask 类型:',
      typeof currentStore.value?.zoomMask
    )
  }
}

// 重置缩放
const resetZoom = () => {
  console.log('[ChangeCustom] 执行重置缩放操作')
  console.log('[ChangeCustom] 当前 store:', currentStore.value)

  // 通知父组件重置缩放操作
  if (
    currentStore.value &&
    typeof currentStore.value.resetZoomMask === 'function'
  ) {
    console.log('[ChangeCustom] 调用 resetZoomMask()')
    currentStore.value.resetZoomMask()
    console.log('[ChangeCustom] 已通知 store 重置缩放')
  } else {
    console.warn('[ChangeCustom] store 中未找到 resetZoomMask 方法')
    console.warn('[ChangeCustom] currentStore.value:', currentStore.value)
    console.warn(
      '[ChangeCustom] resetZoomMask 类型:',
      typeof currentStore.value?.resetZoomMask
    )
  }
}

// 切换拖动模式
const toggleDrag = () => {
  // 1、当前页面
  isDragging.value = !isDragging.value
  console.log('[ChangeCustom] 切换拖动模式:', isDragging.value)
  // console.log('[ChangeCustom] 当前 store:', currentStore.value)

  // 2、通知父组件切换拖动模式
  if (
    currentStore.value &&
    typeof currentStore.value.toggleDragMode === 'function'
  ) {
    console.log('[ChangeCustom] 调用 toggleDragMode(' + isDragging.value + ')')
    currentStore.value.toggleDragMode(isDragging.value)
    console.log('[ChangeCustom] 已通知 store 切换拖动模式:', isDragging.value)
  } else {
    console.warn('[ChangeCustom] store 中未找到 toggleDragMode 方法')
    console.warn('[ChangeCustom] currentStore.value:', currentStore.value)
    console.warn(
      '[ChangeCustom] toggleDragMode 类型:',
      typeof currentStore.value?.toggleDragMode
    )
  }

  // 3、拖拽模式，触发画笔状态的变化
  if (isDragging.value) {
    backupBrushType = brushType.value
    brushType.value = ''
    currentStore.value.updateBrushType('')
  } else {
    brushType.value = backupBrushType
    currentStore.value.updateBrushType(backupBrushType)
    backupBrushType = ''
  }
}

let backupBrushType = '' // 记录画笔类型
watch(
  () => currentStore.value.isOrigin,
  (newOrigin) => {
    if (newOrigin) {
      backupBrushType = brushType.value
      brushType.value = ''
    } else {
      brushType.value = backupBrushType
    }
  }
)

// 监听父组件的历史记录变化
watch(
  () => currentStore.value.maskHistory,
  (newHistory) => {
    console.log('[ChangeCustom] 监听到历史记录变化:', newHistory)
    if (newHistory) {
      history.value = newHistory
      historyIndex.value = newHistory.length - 1
      console.log(
        '[ChangeCustom] 更新本地历史记录, 长度:',
        newHistory.length,
        '当前索引:',
        historyIndex.value
      )
    }
  },
  { deep: true }
)

// 监听当前 store 变化
watch(
  () => currentStore.value,
  (newStore, oldStore) => {
    console.log('[ChangeCustom] 当前 store 发生变化:', {
      oldStore: oldStore?.constructor?.name || 'undefined',
      newStore: newStore?.constructor?.name || 'undefined',
    })
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(() => {
  console.log('[ChangeCustom] 组件挂载，开始初始化')
  console.log(
    '[ChangeCustom] 当前 store:',
    currentStore.value?.constructor?.name || 'undefined'
  )
  console.log('[ChangeCustom] 当前路由:', route.name)

  // 初始化画笔设置
  updateBrushSize(brushSize.value)
  updateEdgeSoftness(edgeSoftness.value)

  console.log('[ChangeCustom] 初始化完成')
})
</script>

<style lang="scss">
.change-custom-component {
  .el-slider {
    flex: 1;
    height: 20px;
    .el-slider__runway {
      height: 3px;
      border-radius: 3px;
      background-color: #e4e4e6;

      .el-slider__bar {
        height: 3px !important;
        border-radius: 3px;
      }

      .el-slider__button {
        position: relative;
        width: 9px;
        height: 9px;
        top: -1.5px;
        border: #1890ff;
        background: #1890ff;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.change-custom-component {
  display: flex;
  flex-direction: column;
  height: 100%;

  .gray {
    filter: opacity(0.5);
    cursor: no-drop !important;
  }

  .title {
    margin: 12px 0;
    font-weight: 600; // electron
    font-size: 14px;
    color: #333333;
    line-height: 20px;
  }

  .brush-types {
    display: flex;
    justify-content: space-between;
    margin-bottom: 14px;

    .brush-type {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;

      img {
        width: 116px;
        height: 116px;
      }
    }
  }

  .brush-setting {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .desc {
      margin-right: 10px;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }

    .value {
      width: 50px;
      text-align: right;
      font-size: 14px;
      color: #666;
    }
  }

  .operations {
    .operation-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;
    }

    .operation-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 42px;
      margin-right: 27px;
      margin-bottom: 10px;
      transition: all 0.3s;

      &:nth-child(4n) {
        margin-right: 0;
      }

      .operation-icon {
        width: 32px;
        height: 32px;
        background-size: contain;
        background-repeat: no-repeat;
        display: inline-block;

        &.undo {
          background-image: url('/img/pic/second2/<EMAIL>');
        }

        &.redo {
          background-image: url('/img/pic/second2/<EMAIL>');
        }

        &.reset {
          background-image: url('/img/pic/second2/<EMAIL>');
        }

        &.zoom-in {
          background-image: url('/img/pic/second2/<EMAIL>');
        }

        &.zoom-out {
          background-image: url('/img/pic/second2/<EMAIL>');
        }

        &.original-ratio {
          background-image: url('/img/pic/second2/<EMAIL>');
        }

        &.drag {
          background-image: url('/img/pic/second2/<EMAIL>');
        }
        &.drag-selected {
          background-image: url('/img/pic/second2/<EMAIL>');
        }
        &.drag-selected + span {
          color: #389bfd;
        }
      }

      span {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        user-select: none;
      }
    }
  }
}
</style>
