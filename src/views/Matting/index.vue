<template>
  <div
    class="Matting"
    :style="{
      backgroundColor: imgsMap.size ? '#f6f6f6' : '#fff',
    }"
  >
    <!-- 抠图首页 -->
    <EntranceHome
      :imgExts="imgWatermarkExts"
      :imgsMap="imgsMap"
      :callback="batchStore.imageInfoHandler"
    />
    <section
      v-if="imgsMap.size"
      v-loading="loading"
      :element-loading-text="''"
      class="detail"
    >
      <MattingHeader :currentStore="batchStore" :imgsMap="batchStore.imgsMap" />
      <!-- 换背景 -->
      <div class="background-box box chessboard-bg">
        <div
          class="background-container"
          :style="{
            background:
              batchStore.currentImg.backgroundColor === 'transparent' ||
              batchStore.currentImg.backgroundImage
                ? ''
                : batchStore.currentImg.backgroundColor,
          }"
        >
          <img
            v-if="batchStore.currentImg.backgroundImage"
            class="background-image"
            :src="batchStore.currentImg.backgroundImage"
          />
          <img
            v-show="batchStore.currentImg.mattingImage"
            class="matting-image"
            ref="mattingImageRef"
            :src="batchStore.currentImg.mattingImage"
            :style="{
              opacity: batchStore.currentImg.record?.opacity,
              transform: `
                translate(${batchStore.currentImg.record?.translate?.x || 0}px, ${batchStore.currentImg.record?.translate?.y || 0}px)
                rotate(${batchStore.currentImg.record?.rotate}deg)
                scale(${
                  batchStore.currentImg.record?.scaleX ||
                  batchStore.currentImg.record?.zoom ||
                  1
                }, ${
                  batchStore.currentImg.record?.scaleY ||
                  batchStore.currentImg.record?.zoom ||
                  1
                })
                scaleX(${batchStore.currentImg.record?.transformX})
                scaleY(${batchStore.currentImg.record?.transformY})
              `,
            }"
          />
          <Moveable
            v-if="batchStore.currentImg.mattingImage"
            ref="moveableRef"
            :target="'.matting-image'"
            :draggable="true"
            :scalable="true"
            :rotatable="true"
            :clickable="true"
            :origin="false"
            :throttleDrag="1"
            :throttleRotate="1"
            :throttleScale="0.01"
            :style="{
              visibility: isMoveableActive ? 'visible' : 'hidden',
            }"
            @drag="onDrag"
            @scale="onScale"
            @rotate="onRotate"
            @dragEnd="onTransformEnd"
            @scaleEnd="onTransformEnd"
            @rotateEnd="onTransformEnd"
            @click="isMoveableActive = true"
          />
        </div>
        <!-- 抠图进度条 -->
        <div v-if="showProgress" class="matting-progress">
          <el-progress type="circle" :percentage="progressValue" />
        </div>

        <FooterTool
          v-show="
            batchStore.currentSegment ===
            batchStore.segmentsType.changeBackground
          "
          :imgsMap="imgsMap"
          :currentStore="batchStore"
        />
      </div>
      <!-- 改尺寸 -->
      <!-- v-if="batchStore.currentSegment === batchStore.segmentsType.changeSize" -->
      <!-- v-if="batchStore.currentImg.mattingCropImage" -->
      <div
        class="cropper-box box"
        :style="{
          zIndex:
            batchStore.currentSegment === batchStore.segmentsType.changeSize
              ? 2
              : 0,
        }"
      >
        <img
          ref="cropperRef"
          :src="batchStore.currentImg.mattingCropImage"
          @load="initCropper"
          @cropend="batchStore.updateCropBoxData"
        />
      </div>
      <!-- 手动精修 -->
      <div
        class="manual-refine-box chessboard-bg box"
        :style="{
          zIndex:
            batchStore.currentSegment === batchStore.segmentsType.custom
              ? 2
              : 0,
        }"
      >
        <!-- 效果图 -->
        <canvas
          v-show="!isOrigin"
          ref="manualRefineCanvas"
          class="manual-refine-canvas"
          :style="{
            width: `${canvasSize.width}px`,
            height: `${canvasSize.height}px`,
            transform: manualCanvasTransform
          }"
          @mousedown="startCanvasDrawing"
          @mousemove="handleCanvasDrawing"
          @mouseup="stopCanvasDrawing"
          @mouseleave="stopCanvasDrawing"
        ></canvas>
        <!-- 原图 -->
        <div
          v-show="isOrigin"
          ref="originBoxRef"
          class="origin-box"
          :style="{
            width: `${canvasSize.width}px`,
            height: `${canvasSize.height}px`,
            transform: manualCanvasTransform
          }"
        >
          <img
            class="origin-matting-img"
            :src="batchStore.currentImg.mattingImage"
            :style="{
              width: '100%',
              height: '100%',
              objectFit: 'contain'
            }"
          />
        </div>
        <!-- Moveable 组件用于手动精修的拖拽和缩放 -->
        <Moveable
          v-if="batchStore.currentSegment === batchStore.segmentsType.custom && (manualRefineCanvas || originBoxRef)"
          ref="manualMoveableRef"
          :target="isOrigin ? '.origin-box' : '.manual-refine-canvas'"
          :draggable="batchStore.maskDragMode"
          :scalable="batchStore.maskDragMode"
          :rotatable="false"
          :clickable="false"
          :origin="false"
          :throttleDrag="0"
          :throttleScale="0"
          :renderDirections="[]"
          :edge="false"
          :zoom="1"
          :hideDefaultLines="true"
          :style="{
            visibility: 'hidden'
          }"
          @drag="onManualDrag"
          @scale="onManualScale"
          @dragEnd="onManualTransformEnd"
          @scaleEnd="onManualTransformEnd"
        />

        <div v-show="batchStore.currentSegment === batchStore.segmentsType.custom" class="flex info">
          <div
            class="origin-info center"
            :class="{
              selected: isOrigin,
            }"
            @click="isOrigin = true"
          >
            原图
          </div>
          <div
            class="result-info center"
            :class="{
              selected: !isOrigin,
            }"
            @click="isOrigin = false"
          >
            效果图
          </div>
        </div>
      </div>
      <!-- 水印 -->
      <div class="watermark-box box">
        <el-watermark
          content="VIP可无水印保存"
          rotate="-50"
          :gap="[50, 50]"
          :font="{
            fontSize: 14,
            fontWeight: 500,
            color: 'rgb(187, 187, 187)',
          }"
          style="color: #fff"
        >
          <div style="height: 4000px" />
        </el-watermark>
      </div>
      <!-- SegmentDrawer -->
      <div class="segment-drawer-wrapper" :class="{ disabled: showProgress }">
        <SegmentDrawer :currentStore="batchStore" />
      </div>
    </section>
    <!-- web才使用 -->
    <InputHidden
      :keepShow="true"
      :multiple="false"
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.chooseWebImages"
    />
    <!-- web才使用 -->
    <InputHidden
      id="customMattingInput"
      :keepShow="true"
      :multiple="true"
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.customChooseWebImages"
    />
    <ExportDirectoryDialog />
    <ConvertSuccessDialog v-if="showConvertSuccessDialog" :showOpen="false" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { useMattingStore } from '@/pinia/matting'
import { imgWatermarkExts, getWebExtsByName } from '@/utils/enum/configData'
import { ElMessage } from 'element-plus'
import EntranceHome from '@/views/Matting/components/EntranceHome.vue'
import { MattingHeader } from '@/components/Matting'
import FooterTool from '@/components/Common/FooterTool.vue'
import SegmentDrawer from '@/components/Batch/SegmentDrawer.vue'
import InputHidden from '@/components/Common/InputHidden.vue'
import ExportDirectoryDialog from '@/components/Common/ExportDirectoryDialog.vue'
import ConvertSuccessDialog from '@/components/Common/ConvertSuccessDialog.vue'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import Moveable from 'vue3-moveable'

const route = useRoute()
const commonStore = useCommonStore()
const batchStore = useMattingStore()
const { proxy } = getCurrentInstance()

// 解构出需要的状态
let {
  imgsMap,
  currentIndex,
  currentImg,
  loading,
  isOrigin,
  option,
  cropper,
  cropperRef,
} = storeToRefs(batchStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)
let isMoveableActive = ref(false)

// 背景盒子的尺寸
const backgroundBoxSize = ref({ width: 0, height: 0 })
// 抠图图片的尺寸
const mattingImageSize = ref({ width: 0, height: 0 })
// 抠图图片元素引用
const mattingImageRef = ref(null)
// Moveable组件引用 - 使用any类型避免TypeScript错误
const moveableRef = ref<any>(null)
// 缓存的matting-image DOM元素引用
const mattingImageElement = ref<HTMLElement | null>(null)

// 手动精修相关
const manualRefineCanvas = ref<HTMLCanvasElement | null>(null) // 手动精修的canvas
const manualRefineCanvasContext = ref<CanvasRenderingContext2D | null>(null) // 缓存手动精修的canvas上下文
const isDrawing = ref(false)
const lastX = ref(0)
const lastY = ref(0)

// 画布尺寸
const canvasSize = ref({ width: 0, height: 0 })

// DOM 元素引用
const originBoxRef = ref<HTMLElement | null>(null)
const manualMoveableRef = ref<any>(null)

// 手动精修更新事件处理函数
const handleMattingImageUpdated = () => {
  console.log('[Matting/index] 收到手动精修更新事件，重新生成组合图')
  if (currentImg.value?.mattingImage) {
    createMattingCropImage()
  }
}

// 手动精修的变换状态
const manualTransform = ref({
  translate: { x: 0, y: 0 },
  scale: { x: 1, y: 1 }
})

// 计算手动精修画布的变换字符串
const manualCanvasTransform = computed(() => {
  const { translate, scale } = manualTransform.value
  return `translate(${translate.x}px, ${translate.y}px) scale(${scale.x}, ${scale.y})`
})

// 初始化
batchStore.init(route.name)
batchStore.batchSaveCallback()

// Vue3-moveable 事件处理函数
const onDrag = (e: any) => {
  if (!currentImg.value) return

  isMoveableActive.value = true

  // 获取拖拽后的变换矩阵
  const transform = e.transform

  // 从transform中提取translate值
  // transform格式通常为: "translate(113px, 90px) rotate(0deg) scale(1, 1)"
  const translateMatch = transform.match(/translate\(([^,]+),\s*([^)]+)\)/)
  if (translateMatch && translateMatch.length >= 3) {
    // 提取数值并去掉单位（如px）
    const translateX = parseFloat(translateMatch[1])
    const translateY = parseFloat(translateMatch[2])

    // 保存到record中
    if (!currentImg.value.record.translate) {
      currentImg.value.record.translate = { x: 0, y: 0 }
    }
    currentImg.value.record.translate.x = translateX
    currentImg.value.record.translate.y = translateY
  }

  // 更新元素样式
  if (mattingImageElement.value) {
    mattingImageElement.value.style.transform = transform
  }
}

const onScale = (e: any) => {
  if (!currentImg.value) return

  // 获取缩放值和变换矩阵
  const scale = e.drag.scale
  const transform = e.drag.transform

  // 更新缩放值 - 保存到 record 中以便持久化
  if (scale && scale.length >= 2) {
    // 分别保存 x 和 y 的缩放值
    currentImg.value.record.scaleX = scale[0]
    currentImg.value.record.scaleY = scale[1]
  } else if (scale && scale.length >= 1) {
    // 兼容旧代码，如果只有一个值，则 x 和 y 相同
    currentImg.value.record.scaleX = scale[0]
    currentImg.value.record.scaleY = scale[0]
  }

  // 更新元素样式
  if (mattingImageElement.value) {
    mattingImageElement.value.style.transform = transform
  }
}

const onRotate = (e: any) => {
  if (!currentImg.value) return

  // 获取旋转角度和变换矩阵
  const rotate = e.rotate
  const transform = e.drag.transform

  // 更新旋转值 - 保存到 record 中以便持久化
  currentImg.value.record.rotate = rotate

  // 更新元素样式
  if (mattingImageElement.value) {
    mattingImageElement.value.style.transform = transform
  }
}

// 变换结束时调用 createMattingCropImage
const onTransformEnd = () => {
  if (currentImg.value?.mattingImage) {
    if (mattingImageElement.value) {
      // 获取当前的transform样式
      const transformStyle = mattingImageElement.value.style.transform

      // 确保 record 对象存在
      if (!currentImg.value.record) {
        currentImg.value.record = {
          opacity: 1,
          rotate: 0,
          zoom: 1,
          scaleX: 1,
          scaleY: 1,
          transformX: 1,
          transformY: 1,
          width: 0,
          height: 0,
          translate: { x: 0, y: 0 },
        }
      }

      // 确保translate对象存在
      if (!currentImg.value.record.translate) {
        currentImg.value.record.translate = { x: 0, y: 0 }
      }

      // 从transform中提取translate值（如果尚未在onDrag中提取）
      const translateMatch = transformStyle.match(
        /translate\(([^,]+),\s*([^)]+)\)/
      )
      if (translateMatch && translateMatch.length >= 3) {
        // 提取数值并去掉单位（如px）
        const translateX = parseFloat(translateMatch[1])
        const translateY = parseFloat(translateMatch[2])

        // 保存到record中
        currentImg.value.record.translate.x = translateX
        currentImg.value.record.translate.y = translateY
      }

      // 从transform中提取scale值
      const scaleMatch = transformStyle.match(/scale\(([^,]+),\s*([^)]+)\)/)
      if (scaleMatch && scaleMatch.length >= 3) {
        // 提取数值
        const scaleX = parseFloat(scaleMatch[1])
        const scaleY = parseFloat(scaleMatch[2])

        // 保存到record中
        currentImg.value.record.scaleX = scaleX
        currentImg.value.record.scaleY = scaleY
        // 兼容旧代码
        currentImg.value.record.zoom = scaleX
      }

      console.log('Transform end with values:', {
        translate: currentImg.value.record.translate,
        rotate: currentImg.value.record.rotate,
        scaleX: currentImg.value.record.scaleX,
        scaleY: currentImg.value.record.scaleY,
      })
    }

    // 生成抠图后的图片与背景的组合图，加上防抖
    // 节约性能且防止改尺寸渲染失败
    proxy?.$filter?.tools.debounce(() => {
      createMattingCropImage()
    }, 300)()
  }
}

// 手动触发Moveable组件更新
const updateMoveableRender = () => {
  if (moveableRef.value) {
    // 强制Moveable组件重新渲染
    moveableRef.value.updateRect()

    if (mattingImageElement.value) {
      moveableRef.value.updateTarget()
    }
  }
}

// 处理文档点击事件，用于取消Moveable选择（未生效）
const handleDocumentClick = (event: MouseEvent) => {
  // 检查点击是否在matting-image元素上
  if (mattingImageElement.value) {
    if (
      !mattingImageElement.value.contains(event.target as Node) &&
      !(event.target as Element).closest('.moveable-control')
    ) {
      // 点击在matting-image元素外部，取消选择
      if (moveableRef.value) {
        isMoveableActive.value = false
      }
    }
  }
}

// 监听 currentIndex 变化
watch(
  () => [currentIndex.value],
  async () => {
    // console.log("currentIndexChange:", currentIndex.value);
    batchStore.getCurrent()
  }
)

// 监听 currentImg 变化
watch(
  () => [currentImg.value],
  async () => {
    try {
      clearCropper()

      // 重置处理器状态和释放资源
      resetState()

      // mark: 等待更长时间确保资源已释放，应该不需要先注释掉
      // await new Promise((resolve) => setTimeout(resolve, 300))

      // 重新初始化处理器
      await initProcessor()

      // 开始抠图
      handleMatting()

      // 当 currentImg 变化时，无论是否有 mattingImage，都需要更新尺寸
      // 这确保了首次加载时也会计算尺寸
      nextTick(() => getBackgroundBoxSize())

      // 缓存matting-image元素引用
      mattingImageElement.value = document.querySelector('.matting-image')
    } catch (error) {
      console.error('Error processing new image:', error)
      handleError(
        'Error processing new image: ' +
          (error instanceof Error ? error.message : String(error))
      )
    }
  }
)

// 监听背景色、背景图片的变化
watch(
  () => [currentImg.value?.backgroundColor, currentImg.value?.backgroundImage],
  () => {
    if (currentImg.value?.mattingImage) {
      // 当背景变化时，重新计算尺寸
      updateMattingImageSize()
        .then(() => {
          // 手动触发 Moveable 组件更新
          updateMoveableRender()
          // 尺寸更新完成后，创建组合图片
          createMattingCropImage()
        })
        .catch((error) => {
          console.error('Error updating matting image size:', error)
        })
    }
  }
)

// 监听图片变换属性的变化
watch(
  () => [
    currentImg.value?.record?.opacity,
    currentImg.value?.record?.rotate,
    currentImg.value?.record?.zoom,
    currentImg.value?.record?.scaleX,
    currentImg.value?.record?.scaleY,
    currentImg.value?.record?.transformX,
    currentImg.value?.record?.transformY,
    currentImg.value?.record?.translate?.x,
    currentImg.value?.record?.translate?.y,
  ],
  () => {
    if (currentImg.value?.mattingImage) {
      // 手动触发 Moveable 组件更新
      updateMoveableRender()

      // 生成抠图后的图片与背景的组合图，加上防抖
      // 节约性能且防止改尺寸渲染失败
      proxy?.$filter?.tools.debounce(() => {
        createMattingCropImage()
      }, 300)()
    }
  }
)

// 监听抠图图片的变化
watch(
  () => currentImg.value?.mattingImage,
  () => {
    if (currentImg.value?.mattingImage) {
      updateMattingImageSize()
    }
  }
)

// const downloadImage = () => {
//   cropper.value?.getCroppedCanvas().toDataURL('image/webp', 0.9)
// }

// 销毁旧的裁剪器实例
const clearCropper = () => {
  if (cropper.value) {
    cropper.value.destroy()
    cropper.value = null
  }
}

// 初始化裁剪器
const initCropper = () => {
  // console.log('initCropper');
  if (cropperRef.value) {
    try {
      cropper.value = new Cropper(cropperRef.value, {
        dragMode: option.value.dragMode as any,
        viewMode: option.value.viewMode as any,
        guides: option.value.guides, // 是否显示网格线
        movable: option.value.movable, // 图片是否可移动
        zoomable: option.value.zoomable, // 是否可以缩放图片（以图片左上角为原点进行缩放）
        autoCrop: option.value.autoCrop, // 是否自动裁剪
        autoCropArea: option.value.autoCropArea, // 默认裁剪区域大小，值为0-1，表示裁剪框占图片的比例，1表示裁剪框与图片一样大
        cropBoxMovable: option.value.cropBoxMovable, // 裁剪框是否可移动
        cropBoxResizable: !option.value.cropBoxResizable, // 裁剪框是否可调整大小
        minCropBoxWidth: option.value.minCropBoxWidth, // 裁剪框最小宽度
        minCropBoxHeight: option.value.minCropBoxHeight, // 裁剪框最小高度

        ready() {
          if (!cropper.value) return
          // console.log(111, currentImg.value?.record)
          if (currentImg.value?.record?.aspectRatio) {
            // console.log(222)
            cropper.value.setAspectRatio(currentImg.value?.record?.aspectRatio)
          }
          if (currentImg.value?.record?.left) {
            // console.log(333)
            cropper.value.setCropBoxData({
              left: currentImg.value?.record?.left || 0,
              top: currentImg.value?.record?.top || 0,
              width: currentImg.value?.record?.autoCropWidth || undefined,
              height: currentImg.value?.record?.autoCropHeight || undefined,
            })
          }
          cropper.value.crop() // 手动触发裁剪框显示
          // console.log(`Cropper 初始化完成`, cropper.value);
          batchStore.updateCropBoxData()
          // batchStore.changeSegmenting = false
        },
      })
    } catch (error) {
      console.error('初始化裁剪器失败:', error)
    }
  }
}

// 处理抠图
const handleMatting = () => {
  try {
    if (!currentImg.value || !currentImg.value.file) {
      ElMessage.warning('请先选择图片')
      return
    }

    // 检查文件大小和尺寸，避免处理过大的图片
    const fileSizeMB = currentImg.value.file.size / (1024 * 1024)
    if (fileSizeMB > 30) {
      ElMessage.warning('图片过大，可能导致内存不足。请选择小于30MB的图片。')
      return
    }

    // 创建一个图像对象来检查尺寸
    const img = new Image()
    img.onload = () => {
      const pixelCount = img.width * img.height
      if (pixelCount > 10000 * 10000) {
        // 如果像素数量超过1亿
        ElMessage.warning(
          '图片尺寸过大，可能导致内存不足。请选择尺寸小一点的图片。'
        )
        return
      }

      // 开始处理图片
      startProcessing()
    }
    img.onerror = () => {
      console.error('Failed to load image for size check')
      // 如果加载失败，仍然尝试处理
      startProcessing()
    }
    img.src = URL.createObjectURL(currentImg.value.file)

    function startProcessing() {
      // 开始抠图处理
      const reader = new FileReader()
      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (e.target && e.target.result) {
          const result = e.target.result as string
          srcImageUrl.value = result
          processImage(result)
        }
      }
      reader.onerror = (error) => {
        console.error('Failed to read file:', error)
        handleError(
          'Failed to read file: ' +
            (error instanceof Error ? error.message : 'Unknown error')
        )
      }
      reader.readAsDataURL(currentImg.value.file)
    }
  } catch (error) {
    console.error('Error in handleMatting:', error)
    handleError(
      'Error processing image: ' +
        (error instanceof Error ? error.message : String(error))
    )
  }
}

/**
 * 创建抠图后的图片与背景的组合图
 * createMattingCropImage
 */
const createMattingCropImage = () => {
  if (!currentImg.value || !currentImg.value.mattingImage) {
    console.error('No matting image available')
    return
  }

  // mark: 当创建新的组合图时，重新计算尺寸，应该不需要先注释掉
  // -> updateMattingImageSize -> mattingCropImage
  // nextTick(() => updateMattingImageSize())

  // 创建一个新的 Image 对象来加载抠图图片
  const mattingImg = new Image()
  mattingImg.crossOrigin = 'anonymous'
  mattingImg.onload = () => {
    if (currentImg.value.backgroundImage) {
      // 如果有背景图片，使用DOM元素的尺寸和位置
      // 获取背景图片和抠图图片的DOM元素
      const backgroundImageEl = document.querySelector('.background-image')
      const mattingImageEl = document.querySelector('.matting-image')

      if (backgroundImageEl && mattingImageEl) {
        // 使用 getComputedStyle 获取实际应用的样式值
        const bgComputedStyle = window.getComputedStyle(backgroundImageEl)
        const mattingComputedStyle = window.getComputedStyle(mattingImageEl)

        // 获取背景图片的尺寸
        const backgroundImageWidth =
          parseInt(bgComputedStyle.width) || backgroundImageEl.clientWidth || 0
        const backgroundImageHeight =
          parseInt(bgComputedStyle.height) ||
          backgroundImageEl.clientHeight ||
          0

        // 获取抠图图片的尺寸和位置
        const mattingWidth =
          parseInt(mattingComputedStyle.width) ||
          mattingImageEl.clientWidth ||
          0
        const mattingHeight =
          parseInt(mattingComputedStyle.height) ||
          mattingImageEl.clientHeight ||
          0
        const left = parseInt(mattingComputedStyle.left) || 0
        const top = parseInt(mattingComputedStyle.top) || 0

        console.log('DOM elements dimensions:', {
          backgroundImageWidth,
          backgroundImageHeight,
          mattingWidth,
          mattingHeight,
          left,
          top,
        })

        // 检查尺寸是否有效
        if (
          backgroundImageWidth <= 0 ||
          backgroundImageHeight <= 0 ||
          mattingWidth <= 0 ||
          mattingHeight <= 0
        ) {
          console.error(
            'Invalid dimensions detected, falling back to original logic'
          )
          return // 这将导致使用原来的逻辑
        }

        // 创建画布，使用背景图片的尺寸，但分辨率提高一倍以提升图片质量
        const canvas = document.createElement('canvas')
        // 将画布尺寸设置为背景图片尺寸的scaleFactor倍，以提高图片质量
        canvas.width = backgroundImageWidth * batchStore.scaleFactor
        canvas.height = backgroundImageHeight * batchStore.scaleFactor
        const ctx = canvas.getContext('2d')

        if (!ctx) {
          console.error('Failed to get canvas context')
          return
        }

        // 缩放绘图上下文以匹配更高的分辨率
        ctx.scale(batchStore.scaleFactor, batchStore.scaleFactor)

        // 加载背景图片
        const bgImg = new Image()
        bgImg.crossOrigin = 'anonymous'
        bgImg.onload = () => {
          // 绘制背景图片，填充整个画布
          // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里的尺寸使用原始值
          ctx.drawImage(
            bgImg,
            0,
            0,
            backgroundImageWidth,
            backgroundImageHeight
          )

          // 保存当前状态
          ctx.save()

          // 设置抠图图片的位置
          // 先移动到抠图图片的左上角，再加上宽高的一半，这样旋转和缩放会围绕图片中心进行
          // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里的坐标也需要使用原始坐标（不需要乘以 scaleFactor）
          // 如果有translate值，则使用translate值
          const translateX = currentImg.value.record?.translate?.x || 0
          const translateY = currentImg.value.record?.translate?.y || 0
          ctx.translate(
            left + mattingWidth / 2 + translateX,
            top + mattingHeight / 2 + translateY
          )

          // 应用旋转
          const rotate = currentImg.value.record?.rotate || 0
          ctx.rotate((rotate * Math.PI) / 180)

          // 应用缩放和翻转
          const scaleX =
            currentImg.value.record?.scaleX ||
            currentImg.value.record?.zoom ||
            1
          const scaleY =
            currentImg.value.record?.scaleY ||
            currentImg.value.record?.zoom ||
            1
          const transformX = currentImg.value.record?.transformX || 1
          const transformY = currentImg.value.record?.transformY || 1
          ctx.scale(scaleX * transformX, scaleY * transformY)

          // 应用透明度
          ctx.globalAlpha = currentImg.value.record?.opacity || 1

          // 绘制抠图图片（从中心点偏移回原位置）
          // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里的坐标和尺寸也使用原始值
          ctx.drawImage(
            mattingImg,
            -mattingWidth / 2,
            -mattingHeight / 2,
            mattingWidth,
            mattingHeight
          )

          // 恢复之前保存的状态
          ctx.restore()
          // 将组合后的图片保存到 store
          clearCropper()
          // 使用更高质量的设置导出图片
          currentImg.value.mattingCropImage = canvas.toDataURL(
            'image/png',
            0.95
          )
        }
        bgImg.src = currentImg.value.backgroundImage
        return
      }
    } else {
      // 如果没有背景图片或者无法获取DOM元素
      // 创建画布，使用更高的分辨率提升图片质量
      const canvas = document.createElement('canvas')
      const scaleFactor = 2
      canvas.width = mattingImg.width * scaleFactor
      canvas.height = mattingImg.height * scaleFactor
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        console.error('Failed to get canvas context')
        return
      }

      // 缩放绘图上下文以匹配更高的分辨率
      ctx.scale(scaleFactor, scaleFactor)

      // 保存当前的变换状态
      ctx.save()

      // 如果有背景色，先填充背景色
      if (
        currentImg.value.backgroundColor &&
        currentImg.value.backgroundColor !== 'transparent'
      ) {
        ctx.fillStyle = currentImg.value.backgroundColor
        // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里需要使用原始尺寸
        ctx.fillRect(0, 0, mattingImg.width, mattingImg.height)
      }

      // 应用变换前先将坐标系移到画布中心
      // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里需要使用原始尺寸的中心点
      // 如果有translate值，则使用translate值
      const translateX = currentImg.value.record?.translate?.x || 0
      const translateY = currentImg.value.record?.translate?.y || 0
      ctx.translate(
        mattingImg.width / 2 + translateX,
        mattingImg.height / 2 + translateY
      )

      // 应用旋转
      const rotate = currentImg.value.record?.rotate || 0
      ctx.rotate((rotate * Math.PI) / 180)

      // 应用缩放和翻转
      const scaleX =
        currentImg.value.record?.scaleX || currentImg.value.record?.zoom || 1
      const scaleY =
        currentImg.value.record?.scaleY || currentImg.value.record?.zoom || 1
      const transformX = currentImg.value.record?.transformX || 1
      const transformY = currentImg.value.record?.transformY || 1
      ctx.scale(scaleX * transformX, scaleY * transformY)

      // 应用透明度
      ctx.globalAlpha = currentImg.value.record?.opacity || 1

      // 绘制抠图图片 (从中心点偏移回原位置)
      // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里的坐标和尺寸也使用原始值
      ctx.drawImage(
        mattingImg,
        -mattingImg.width / 2,
        -mattingImg.height / 2,
        mattingImg.width,
        mattingImg.height
      )

      // 恢复之前保存的状态
      ctx.restore()
      // 将组合后的图片保存到 store
      clearCropper()
      // 使用更高质量的设置导出图片
      currentImg.value.mattingCropImage = canvas.toDataURL('image/png', 0.95)
    }
  }
  mattingImg.onerror = (error) => {
    console.error('Failed to load matting image:', error)
  }
  mattingImg.src = currentImg.value.mattingImage
}
/**
 * 获取背景盒子的尺寸
 * getBackgroundBoxSize -> updateMattingImageSize
 */
const getBackgroundBoxSize = () => {
  console.log('getBackgroundBoxSize')
  const backgroundBox = document.querySelector('.background-box')
  if (backgroundBox) {
    backgroundBoxSize.value = {
      width: Math.round(backgroundBox.clientWidth),
      height: Math.round(backgroundBox.clientHeight),
    }
    console.log('Background box size:', backgroundBoxSize.value)

    // 更新抠图图片尺寸
    updateMattingImageSize()
  }
}
/**
 * 更新抠图、背景图的尺寸
 * dom结构：
 *   background-box
 *     background-container
 *       background-image
 *       matting-image
 * @returns {Promise<void>} 返回一个Promise，当所有图片加载和处理完成时resolve
 */
const updateMattingImageSize = (): Promise<void> => {
  console.log('createMattingCropImage')
  if (!backgroundBoxSize.value.width || !backgroundBoxSize.value.height) {
    return Promise.resolve() // 如果没有有效的背景盒子尺寸，立即resolve
  }

  return new Promise<void>((resolve) => {
    // 如果存在背景图片，先计算背景图片的尺寸
    if (currentImg.value?.backgroundImage) {
      // 先计算背景图片的尺寸
      const bgImg = new Image()
      bgImg.onload = () => {
        const bgWidth = bgImg.width
        const bgHeight = bgImg.height
        const bgAspectRatio = bgWidth / bgHeight

        // 计算背景图片的尺寸
        let backgroundImageWidth, backgroundImageHeight

        // 确保背景图片可以撑满背景盒子(两处代码功能一样)
        if (bgWidth >= bgHeight) {
          // 当背景图片宽大于等于高时，宽度等于背景盒子宽度，高度按比例计算
          backgroundImageWidth = Math.round(backgroundBoxSize.value.width)
          backgroundImageHeight = Math.round(
            backgroundImageWidth / bgAspectRatio
          )

          // 确保高度不超过背景盒子高度
          if (backgroundImageHeight > backgroundBoxSize.value.height) {
            backgroundImageHeight = Math.round(backgroundBoxSize.value.height)
            backgroundImageWidth = Math.round(
              backgroundImageHeight * bgAspectRatio
            )
          }
        } else {
          // 当背景图片宽小于高时，高度等于背景盒子高度，宽度按比例计算
          backgroundImageHeight = Math.round(backgroundBoxSize.value.height)
          backgroundImageWidth = Math.round(
            backgroundImageHeight * bgAspectRatio
          )

          // 确保宽度不超过背景盒子宽度
          if (backgroundImageWidth > backgroundBoxSize.value.width) {
            backgroundImageWidth = Math.round(backgroundBoxSize.value.width)
            backgroundImageHeight = Math.round(
              backgroundImageWidth / bgAspectRatio
            )
          }
        }

        console.log('Background image size:', {
          width: backgroundImageWidth,
          height: backgroundImageHeight,
        })

        // 设置背景图片的尺寸
        const backgroundImage = document.querySelector('.background-image')
        if (backgroundImage) {
          backgroundImage.setAttribute(
            'style',
            `width: ${backgroundImageWidth}px; height: ${backgroundImageHeight}px;`
          )
        }

        // 如果有 mattingImage，计算它的尺寸
        if (currentImg.value?.mattingImage) {
          const mattingImg = new Image()
          mattingImg.onload = () => {
            const mattingWidth = mattingImg.width
            const mattingHeight = mattingImg.height
            const mattingAspectRatio = mattingWidth / mattingHeight

            // 计算 matting-image 的尺寸
            let newWidth, newHeight

            // 当 background-image 的宽小于 matting-image 宽时
            if (backgroundImageWidth < mattingWidth) {
              newWidth = backgroundImageWidth
              newHeight = Math.round(newWidth / mattingAspectRatio)
              console.log(
                'background-image width < matting-image width, adjusting matting-image width'
              )
            }
            // 当 background-image 的高小于 matting-image 高时
            else if (backgroundImageHeight < mattingHeight) {
              newHeight = backgroundImageHeight
              newWidth = Math.round(newHeight * mattingAspectRatio)
              console.log(
                'background-image height < matting-image height, adjusting matting-image height'
              )
            }
            // 如果背景图片尺寸都大于 matting-image，保持 matting-image 原始尺寸
            else {
              newWidth = mattingWidth
              newHeight = mattingHeight
              console.log('Using original matting-image dimensions')
            }

            // 更新尺寸
            mattingImageSize.value = {
              width: Math.round(newWidth),
              height: Math.round(newHeight),
            }
            console.log('Matting image size:', {
              width: Math.round(mattingImageSize.value.width),
              height: Math.round(mattingImageSize.value.height),
            })

            // 应用新尺寸到 background-container 元素
            applyMattingImageSize()

            // 设置 matting-image 的具体像素值和居中样式
            const mattingImage = document.querySelector('.matting-image')
            if (mattingImage) {
              const left = Math.round((backgroundImageWidth - newWidth) / 2)
              const top = Math.round((backgroundImageHeight - newHeight) / 2)

              const currentStyle = mattingImage.getAttribute('style') || ''
              const mattingStyleWithoutDimensions = currentStyle.replace(
                /left:\s*[^;]+;?|top:\s*[^;]+;?|width:\s*[^;]+;?|height:\s*[^;]+;?/g,
                ''
              )
              mattingImage.setAttribute(
                'style',
                `left: ${left}px; top: ${top}px; width: ${Math.round(
                  newWidth
                )}px; height: ${Math.round(
                  newHeight
                )}px; ${mattingStyleWithoutDimensions}`
              )
            }

            // 所有处理完成，resolve Promise
            resolve()
          }
          mattingImg.onerror = () => {
            console.error('Failed to load matting image')
            resolve() // 即使加载失败也resolve，以避免Promise挂起
          }
          mattingImg.src = currentImg.value.mattingImage
        } else {
          resolve() // 如果没有mattingImage，直接resolve
        }
      }
      bgImg.onerror = () => {
        console.error('Failed to load background image')
        resolve() // 即使加载失败也resolve，以避免Promise挂起
      }
      bgImg.src = currentImg.value.backgroundImage
    }
    // 如果没有背景图片，使用原来的逻辑
    else if (currentImg.value?.mattingImage) {
      // 创建一个图像对象来获取原始尺寸
      const img = new Image()
      img.onload = () => {
        const imgWidth = img.width
        const imgHeight = img.height
        const aspectRatio = imgWidth / imgHeight

        // 计算新的尺寸，精确到整数位
        let newWidth, newHeight

        if (imgWidth >= imgHeight) {
          // 当图片宽大于等于高时，宽度等于背景盒子宽度，高度按比例计算
          newWidth = Math.round(backgroundBoxSize.value.width)
          newHeight = Math.round(newWidth / aspectRatio)

          // 确保高度不超过背景盒子高度
          if (newHeight > backgroundBoxSize.value.height) {
            console.log('Adjusting height to fit within background-box')
            newHeight = Math.round(backgroundBoxSize.value.height)
            newWidth = Math.round(newHeight * aspectRatio)
          }
        } else {
          // 当图片宽小于高时，高度等于背景盒子高度，宽度按比例计算
          newHeight = Math.round(backgroundBoxSize.value.height)
          newWidth = Math.round(newHeight * aspectRatio)

          // 确保宽度不超过背景盒子宽度
          if (newWidth > backgroundBoxSize.value.width) {
            console.log('Adjusting width to fit within background-box')
            newWidth = Math.round(backgroundBoxSize.value.width)
            newHeight = Math.round(newWidth / aspectRatio)
          }
        }

        // 更新尺寸
        mattingImageSize.value = {
          width: newWidth,
          height: newHeight,
        }
        console.log('Matting image size:', {
          width: Math.round(mattingImageSize.value.width),
          height: Math.round(mattingImageSize.value.height),
        })

        // 应用新尺寸到 background-container 元素
        applyMattingImageSize()

        // 所有处理完成，resolve Promise
        resolve()
      }
      img.onerror = () => {
        console.error('Failed to load matting image')
        resolve() // 即使加载失败也resolve，以避免Promise挂起
      }
      img.src = currentImg.value.mattingImage
    } else {
      resolve() // 如果既没有背景图片也没有mattingImage，直接resolve
    }
  })
}
// 应用抠图图片的尺寸到 DOM
const applyMattingImageSize = () => {
  const backgroundContainer = document.querySelector('.background-container')
  if (backgroundContainer && currentImg.value?.backgroundImage) {
    return backgroundContainer.setAttribute('style', '') // 清除之前的样式
  }

  // 如果没有背景图片，保持现有逻辑不变
  if (
    backgroundContainer &&
    mattingImageSize.value.width &&
    mattingImageSize.value.height
  ) {
    // 设置 background-container 的尺寸，确保是整数
    const existingStyle = backgroundContainer.getAttribute('style') || ''
    const styleWithoutDimensions = existingStyle.replace(
      /width:\s*[^;]+;?|height:\s*[^;]+;?/g,
      ''
    )
    const roundedWidth = Math.round(mattingImageSize.value.width)
    const roundedHeight = Math.round(mattingImageSize.value.height)
    backgroundContainer.setAttribute(
      'style',
      `width: ${roundedWidth}px; height: ${roundedHeight}px; ${styleWithoutDimensions}`
    )

    // 如果有 matting-image 元素，但没有背景图片，使用百分比尺寸
    const mattingImage = document.querySelector('.matting-image')
    if (mattingImage) {
      const currentStyle = mattingImage.getAttribute('style') || ''
      const mattingStyleWithoutDimensions = currentStyle.replace(
        /left:\s*[^;]+;?|top:\s*[^;]+;?|width:\s*[^;]+;?|height:\s*[^;]+;?/g,
        ''
      )
      mattingImage.setAttribute(
        'style',
        `left: auto; top: auto; width: 100%; height: 100%; ${mattingStyleWithoutDimensions}`
      )
    }
    // 注意：当有背景图片时，在 updateMattingImageSize 函数中直接设置了 matting-image 的具体像素值

    // console.log('Applied new dimensions to background-container:', {
    //   width: roundedWidth,
    //   height: roundedHeight
    // })
  }
}

// 处理窗口大小变化
const handleResize = () => {
  getBackgroundBoxSize()

  // 如果当前在手动精修模式，重新计算画布尺寸
  if (batchStore.currentSegment === batchStore.segmentsType.custom) {
    console.log('[Matting/index] 窗口大小变化，重新计算画布尺寸')
    nextTick(() => {
      calculateCanvasSize()
    })
  }
}

// // 创建一个 MutationObserver 来监听 background-box 元素的变化
// let backgroundBoxObserver: MutationObserver | null = null
// const observeBackgroundBox = () => {
//   // 如果已经有一个观察者，先断开连接
//   if (backgroundBoxObserver) {
//     backgroundBoxObserver.disconnect()
//   }

//   // 获取 background-box 元素
//   const backgroundBox = document.querySelector('.background-box')
//   if (!backgroundBox) return

//   // 创建新的观察者
//   backgroundBoxObserver = new MutationObserver(() => getBackgroundBoxSize())

//   // 开始观察
//   backgroundBoxObserver.observe(backgroundBox, {
//     attributes: true,
//     childList: true,
//     subtree: true,
//     attributeFilter: ['style', 'class']
//   })
// }

// ================ 手动精修相关函数 ================
// 计算画布尺寸，使其与 matting-image 保持一致
const calculateCanvasSize = () => {
  console.log('[Matting/index] 开始计算画布尺寸')

  if (!currentImg.value?.mattingImage) {
    console.warn('[Matting/index] 无法计算画布尺寸，缺少抠图图像')
    return
  }

  // 获取容器尺寸
  const container = document.querySelector('.manual-refine-box') as HTMLElement
  if (!container) {
    console.warn('[Matting/index] 无法找到容器元素')
    return
  }

  const containerRect = container.getBoundingClientRect()
  console.log('[Matting/index] 容器尺寸:', containerRect.width, 'x', containerRect.height)

  // 加载图像获取原始尺寸
  const img = new Image()
  img.crossOrigin = 'anonymous'
  img.onload = () => {
    console.log('[Matting/index] 图像原始尺寸:', img.width, 'x', img.height)

    // == 根据图片宽高缩放到容器尺寸 ==
    // 计算缩放比例，保持宽高比
    const scaleX = containerRect.width / img.width
    const scaleY = containerRect.height / img.height
    const scale = Math.min(scaleX, scaleY)
    // 计算显示尺寸
    const displayWidth = Math.round(img.width * scale) // + 1
    const displayHeight = Math.round(img.height * scale) // + 1
    console.log('[Matting/index] 计算得到的显示尺寸:', displayWidth, 'x', displayHeight)
    // == 根据图片宽高缩放到容器尺寸 ==

    // 更新画布尺寸状态
    canvasSize.value = {
      width: displayWidth,
      height: displayHeight
    }

    // 重置位置和缩放
    manualTransform.value = {
      translate: { x: 0, y: 0 },
      scale: { x: 1, y: 1 }
    }
    // 同步重置 store 中的缩放值
    batchStore.maskZoom = 1

    console.log('[Matting/index] 画布尺寸状态已更新')
  }
  img.onerror = () => {
    console.error('[Matting/index] 图像加载失败，无法计算尺寸')
  }
  img.src = currentImg.value.mattingImage
}

// 初始化画布
const initCanvas = () => {
  console.log('[Matting/index] 开始初始化画布')

  if (!manualRefineCanvas.value || !currentImg.value?.mattingImage || !currentImg.value?.file) {
    console.warn('[Matting/index] 无法初始化画布:', {
      canvas: !!manualRefineCanvas.value,
      mattingImage: !!currentImg.value?.mattingImage,
      originalFile: !!currentImg.value?.file,
    })
    return
  }

  // 首先计算画布尺寸
  calculateCanvasSize()

  const canvas = manualRefineCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    console.error('[Matting/index] 无法获取画布上下文')
    return
  }

  manualRefineCanvasContext.value = ctx
  console.log('[Matting/index] 画布上下文已设置')

  // 加载原始图像 (currentImg.file)
  const originalImg = new Image()
  originalImg.crossOrigin = 'anonymous'
  originalImg.onload = () => {
    console.log('[Matting/index] 原始图像加载完成, 尺寸:', originalImg.width, 'x', originalImg.height)

    // 设置画布内部尺寸
    canvas.width = originalImg.width
    canvas.height = originalImg.height
    console.log('[Matting/index] 画布内部尺寸已设置为:', canvas.width, 'x', canvas.height)

    // 0. 完全清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    console.log('[Matting/index] 画布已完全清空')

    // 1. 首先用原始图像填满画布（作为背景）
    ctx.drawImage(originalImg, 0, 0)
    console.log('[Matting/index] 原始图像已填满画布作为背景')

    // 2. 根据抠图图片，在人像区域添加红色相对透明蒙层
    addPortraitRedOverlay(ctx, originalImg.width, originalImg.height)
  }
  originalImg.onerror = () => {
    console.error('[Matting/index] 原始图像加载失败')
  }

  // 使用原始文件创建 URL
  if (currentImg.value.file instanceof File) {
    originalImg.src = URL.createObjectURL(currentImg.value.file)
  } else {
    console.error('[Matting/index] 原始文件不是有效的 File 对象')
  }
}

// 在人像区域添加红色相对透明蒙层（基于当前蒙版）
const addPortraitRedOverlay = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
  console.log('[Matting/index] 开始基于当前蒙版添加红色蒙层')

  // 使用 mattingImage 来确定人像区域（基于透明度）
  if (!currentImg.value.mattingImage) {
    console.warn('[Matting/index] 没有抠图图像，无法添加红色蒙层')
    return
  }

  // 加载抠图图像
  const mattingImg = new Image()
  mattingImg.crossOrigin = 'anonymous'
  mattingImg.onload = () => {
    console.log('[Matting/index] 抠图图像加载完成，开始分析并添加红色蒙层')

    // 创建临时画布来处理抠图图像
    const tempCanvas = document.createElement('canvas')
    tempCanvas.width = width
    tempCanvas.height = height
    const tempCtx = tempCanvas.getContext('2d')
    if (!tempCtx) return

    // 绘制抠图图像到临时画布，显式匹配画布尺寸
    tempCtx.drawImage(mattingImg, 0, 0, width, height)

    // 获取抠图图像的像素数据
    const imageData = tempCtx.getImageData(0, 0, width, height)
    const imagePixels = imageData.data

    // 创建红色蒙层画布
    const overlayCanvas = document.createElement('canvas')
    overlayCanvas.width = width
    overlayCanvas.height = height
    const overlayCtx = overlayCanvas.getContext('2d')
    if (!overlayCtx) return

    // 创建蒙层的像素数据
    const overlayData = overlayCtx.createImageData(width, height)
    const overlayPixels = overlayData.data

    // 遍历每个像素，在人像区域（不透明区域）创建红色相对透明蒙层
    for (let i = 0; i < imagePixels.length; i += 4) {
      const alpha = imagePixels[i + 3]
      // 只保留 Alpha 较高的核心人像区域，避免边缘半透明像素
      const isPortraitArea = alpha > 128

      if (isPortraitArea) {
        // 在人像区域设置红色相对透明蒙层 (#F60000 相对透明)
        overlayPixels[i] = 246     // R
        overlayPixels[i + 1] = 0   // G
        overlayPixels[i + 2] = 0   // B
        // 根据原始alpha值动态调整蒙层透明度，实现边缘羽化效果
        overlayPixels[i + 3] = Math.min(77, Math.floor(alpha * 0.3))
      } else {
        // 非人像区域保持透明
        overlayPixels[i] = 0
        overlayPixels[i + 1] = 0
        overlayPixels[i + 2] = 0
        overlayPixels[i + 3] = 0
      }
    }

    // 将蒙层数据绘制到蒙层画布
    overlayCtx.putImageData(overlayData, 0, 0)

    // 设置混合模式为 source-over，确保正确叠加
    ctx.globalCompositeOperation = 'source-over'

    // 将红色蒙层绘制到主画布上（叠加在原始图像上）
    ctx.drawImage(overlayCanvas, 0, 0)

    console.log('[Matting/index] 红色蒙层已添加到人像区域')
  }

  mattingImg.onerror = () => {
    console.error('[Matting/index] 抠图图像加载失败')
  }

  mattingImg.src = currentImg.value.mattingImage
}

// 绘制红色透明蒙层
const drawRedMask = (x: number, y: number) => {
  const ctx: any = manualRefineCanvasContext.value

  // 设置画笔样式
  ctx.lineJoin = 'round'
  ctx.lineCap = 'round'
  ctx.lineWidth = batchStore.brushSize

  // 根据画笔类型设置颜色和混合模式
  if (batchStore.brushType === 'keep') {
    // 保留模式：增加红色相对透明蒙层
    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = 'rgba(246, 0, 0, 0.3)' // #F60000 相对透明
  } else {
    // 擦除模式：移除红色蒙层部分
    ctx.globalCompositeOperation = 'destination-out'
    ctx.strokeStyle = 'rgba(0, 0, 0, 1)' // 完全不透明用于擦除
  }

  // 设置边缘柔和度
  if (batchStore.edgeSoftness > 0) {
    ctx.shadowColor = batchStore.brushType === 'keep' ? 'rgba(246, 0, 0, 0.3)' : 'rgba(0, 0, 0, 1)'
    ctx.shadowBlur = batchStore.edgeSoftness
  } else {
    ctx.shadowBlur = 0
  }

  // 开始绘制连续线条
  ctx.beginPath()
  ctx.moveTo(lastX.value, lastY.value)
  ctx.lineTo(x, y)
  ctx.stroke()

  // 重置设置
  ctx.shadowBlur = 0
  ctx.globalCompositeOperation = 'source-over'
  ctx.globalAlpha = 1.0 // 重置透明度
}

// 新的绘制函数，只处理绘制，不处理拖动
const startCanvasDrawing = (e: MouseEvent) => {
  console.log('[Matting/index] 开始画布绘制')

  if (!manualRefineCanvasContext.value || batchStore.maskDragMode) {
    console.log('[Matting/index] 跳过绘制:', {
      manualRefineCanvasContext: !!manualRefineCanvasContext.value,
      maskDragMode: batchStore.maskDragMode,
    })
    return
  }

  isDrawing.value = true

  // 获取鼠标相对于画布的位置
  const rect = manualRefineCanvas.value?.getBoundingClientRect()
  if (!rect) {
    console.error('[Matting/index] 无法获取画布边界信息')
    return
  }

  // 计算缩放比例
  const scaleX = manualRefineCanvas.value!.width / rect.width
  const scaleY = manualRefineCanvas.value!.height / rect.height

  const startX = (e.clientX - rect.left) * scaleX
  const startY = (e.clientY - rect.top) * scaleY

  // 更新最后的位置
  lastX.value = startX
  lastY.value = startY

  drawRedMask(startX, startY) // 位置不能更换
}

const handleCanvasDrawing = (e: MouseEvent) => {
  if (!isDrawing.value || !manualRefineCanvasContext.value || !manualRefineCanvas.value || batchStore.maskDragMode) {
    return
  }

  // 获取鼠标相对于画布的位置
  const rect = manualRefineCanvas.value.getBoundingClientRect()
  if (!rect) return

  // 计算缩放比例
  const scaleX = manualRefineCanvas.value.width / rect.width
  const scaleY = manualRefineCanvas.value.height / rect.height

  const x = (e.clientX - rect.left) * scaleX
  const y = (e.clientY - rect.top) * scaleY

  drawRedMask(x, y) // 位置不能更换

  // 更新最后的位置
  lastX.value = x
  lastY.value = y

}

const stopCanvasDrawing = () => {
  console.log('[Matting/index] 停止画布绘制')

  if (isDrawing.value && manualRefineCanvasContext.value && manualRefineCanvas.value) {
    isDrawing.value = false

    // 从画布提取蒙层信息并更新到 mattingImage
    updateMattingImageFromCanvas()

    // 确保历史记录被正确保存
    console.log('[Matting/index] 画笔操作完成，历史记录应已保存')
  }
}

// 从画布提取蒙层信息并更新 mattingImage（增量更新）
const updateMattingImageFromCanvas = () => {
  if (!manualRefineCanvasContext.value || !manualRefineCanvas.value || !currentImg.value?.mattingImage || !currentImg.value?.file) {
    console.warn('[Matting/index] 无法更新 mattingImage，缺少必要条件')
    return
  }

  console.log('[Matting/index] 开始从画布提取蒙层信息（增量更新）')

  const canvas = manualRefineCanvas.value
  const ctx = manualRefineCanvasContext.value

  // 获取画布的像素数据
  const canvasData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const canvasPixels = canvasData.data

  // 创建新的 mattingImage
  const mattingCanvas = document.createElement('canvas')
  mattingCanvas.width = canvas.width
  mattingCanvas.height = canvas.height
  const mattingCtx = mattingCanvas.getContext('2d')
  if (!mattingCtx) return

  // 同时加载当前的 mattingImage 和原始图像
  const currentMattingImg = new Image()
  const originalImg = new Image()

  let mattingLoaded = false
  let originalLoaded = false

    // 更精确的红色蒙层检测函数
  const hasRedOverlayFN = (pixels: any, i: number) => {
    // 获取当前像素的RGBA值
    const r = pixels[i]
    const g = pixels[i + 1]
    const b = pixels[i + 2]
    const a = pixels[i + 3]

    // 计算颜色强度总和
    const totalIntensity = r + g + b

    // 如果完全透明或几乎没有颜色，返回false
    if (a < 10 || totalIntensity < 10) return false

    // 计算红色占比（0-1之间）
    const redRatio = r / totalIntensity

    // 计算绿色通道与红色通道的比例差
    const greenDiff = Math.max(0, (r - g) / r)

    // 计算蓝色通道与红色通道的比例差
    const blueDiff = Math.max(0, (r - b) / r)

    // 综合判断条件
    return (
      // redRatio > 0.45 &&    // 红色占比超过45%
      // greenDiff > 0.3 &&    // 绿色比红色低至少30%
      // blueDiff > 0.3 &&     // 蓝色比红色低至少30%
      redRatio > 0.25 &&
      greenDiff > 0.15 &&
      blueDiff > 0.15 &&
      a > 20 // 不完全透明
    )
  }

  const processWhenBothLoaded = () => {
    if (!mattingLoaded || !originalLoaded) return

    // 绘制当前的 mattingImage
    mattingCtx.drawImage(currentMattingImg, 0, 0)

    // 获取当前 mattingImage 的像素数据
    const mattingData = mattingCtx.getImageData(0, 0, canvas.width, canvas.height)
    const mattingPixels = mattingData.data

    // 获取原始图像的像素数据
    const originalCanvas = document.createElement('canvas')
    originalCanvas.width = canvas.width
    originalCanvas.height = canvas.height
    const originalCtx = originalCanvas.getContext('2d')
    if (!originalCtx) return

    originalCtx.drawImage(originalImg, 0, 0)
    const originalData = originalCtx.getImageData(0, 0, canvas.width, canvas.height)
    const originalPixels = originalData.data

    // 根据红色蒙层信息增量更新 alpha 通道
    for (let i = 0; i < canvasPixels.length; i += 4) {
      // 检查当前像素是否有红色蒙层（更宽松的检测条件）
      // const hasRedOverlay = canvasPixels[i] > 100 && canvasPixels[i + 1] < 100 && canvasPixels[i + 2] < 100 && canvasPixels[i + 3] > 0

      const hasRedOverlay = hasRedOverlayFN(canvasPixels, i)

      if (hasRedOverlay) {
        // 有红色蒙层的区域：设为不透明（保留人像）
        mattingPixels[i + 3] = 255
        // 如果原来是透明的，从原始图像恢复颜色信息
        if (mattingPixels[i + 3] === 0 || (mattingPixels[i] === 0 && mattingPixels[i + 1] === 0 && mattingPixels[i + 2] === 0)) {
          mattingPixels[i] = originalPixels[i]     // R
          mattingPixels[i + 1] = originalPixels[i + 1] // G
          mattingPixels[i + 2] = originalPixels[i + 2] // B
        }
      }
      // 擦除区域的处理：检查是否画布上该区域已被擦除
      else if (canvasPixels[i + 3] === 0 && mattingPixels[i + 3] > 0) {
        // 如果画布上是透明的，但 mattingImage 中不透明，说明被擦除了
        mattingPixels[i + 3] = 0
      }
    }

    // 将处理后的数据放回画布
    mattingCtx.putImageData(mattingData, 0, 0)

    // 更新 mattingImage
    currentImg.value.mattingImage = mattingCanvas.toDataURL('image/png')
    console.log('[Matting/index] mattingImage 已增量更新')

    // 同步更新 mattingCropImage
    createMattingCropImage()

    // 创建对应的蒙版图像（用于历史记录）
    const maskCanvas = document.createElement('canvas')
    maskCanvas.width = canvas.width
    maskCanvas.height = canvas.height
    const maskCtx = maskCanvas.getContext('2d')
    if (maskCtx) {
      const maskData = maskCtx.createImageData(canvas.width, canvas.height)
      const maskPixels = maskData.data

      // 基于最终的 mattingImage 生成蒙版
      for (let i = 0; i < mattingPixels.length; i += 4) {
        if (mattingPixels[i + 3] > 0) {
          // 不透明区域设为白色（保留）
          maskPixels[i] = 255
          maskPixels[i + 1] = 255
          maskPixels[i + 2] = 255
          maskPixels[i + 3] = 255
        } else {
          // 透明区域设为黑色
          maskPixels[i] = 0
          maskPixels[i + 1] = 0
          maskPixels[i + 2] = 0
          maskPixels[i + 3] = 255
        }
      }

      maskCtx.putImageData(maskData, 0, 0)
      const maskDataURL = maskCanvas.toDataURL('image/png')
      currentImg.value.mattingMaskImage = maskDataURL

      // 添加到历史记录
      batchStore.addMaskHistory(maskDataURL)
      console.log('[Matting/index] 蒙版历史记录已更新')
    }
  }

  currentMattingImg.crossOrigin = 'anonymous'
  currentMattingImg.onload = () => {
    mattingLoaded = true
    processWhenBothLoaded()
  }
  currentMattingImg.src = currentImg.value.mattingImage

  originalImg.crossOrigin = 'anonymous'
  originalImg.onload = () => {
    originalLoaded = true
    processWhenBothLoaded()
  }

  // 使用原始文件创建 URL
  if (currentImg.value.file instanceof File) {
    originalImg.src = URL.createObjectURL(currentImg.value.file)
  }
}

// Moveable 事件处理函数
const onManualDrag = (e: any) => {
  console.log('[Matting/index] 手动精修拖拽:', e.transform)

  // 从 transform 中提取 translate 值
  const translateMatch = e.transform.match(/translate\(([^,]+),\s*([^)]+)\)/)
  if (translateMatch && translateMatch.length >= 3) {
    const translateX = parseFloat(translateMatch[1])
    const translateY = parseFloat(translateMatch[2])

    manualTransform.value.translate = { x: translateX, y: translateY }
    console.log('[Matting/index] 更新拖拽位置:', manualTransform.value.translate)
  }
}

const onManualScale = (e: any) => {
  console.log('[Matting/index] 手动精修缩放:', e.transform)

  // 从 transform 中提取 scale 值
  const scaleMatch = e.transform.match(/scale\(([^,]+),\s*([^)]+)\)/)
  if (scaleMatch && scaleMatch.length >= 3) {
    const scaleX = parseFloat(scaleMatch[1])
    const scaleY = parseFloat(scaleMatch[2])

    manualTransform.value.scale = { x: scaleX, y: scaleY }
    console.log('[Matting/index] 更新缩放比例:', manualTransform.value.scale)

    // 同步更新 store 中的缩放值
    batchStore.maskZoom = Math.max(scaleX, scaleY)
  }
}

const onManualTransformEnd = () => {
  console.log('[Matting/index] 手动精修变换结束')

  // 更新 Moveable 组件
  if (manualMoveableRef.value) {
    manualMoveableRef.value.updateRect()
  }
}

// 监听当前分段变化，初始化画布
watch(
  () => batchStore.currentSegment,
  (newSegment, oldSegment) => {
    console.log('[Matting/index] 分段变化:', {
      oldSegment,
      newSegment,
      customType: batchStore.segmentsType.custom,
    })

    if (newSegment === batchStore.segmentsType.custom) {
      console.log('[Matting/index] 切换到手动精修模式，准备初始化画布')
      // 当切换到手动精修模式时，初始化画布
      nextTick(() => {
        initCanvas()
      })
    }
  }
)

// 移除画布尺寸变化监听器，避免死循环
// 画布尺寸只在初始化和窗口大小变化时计算
// 监听蒙版图像变化，重新初始化画布（使用新的显示模式）
// watch(
//   () => currentImg.value?.mattingMaskImage,
//   (newMaskImage) => {
//     console.log('[Matting/index] 蒙版图像发生变化，重新初始化画布:', !!newMaskImage)

//     if (
//       newMaskImage &&
//       manualRefineCanvasContext.value &&
//       manualRefineCanvas.value &&
//       batchStore.currentSegment === batchStore.segmentsType.custom &&
//       currentImg.value?.file &&
//       currentImg.value?.mattingImage
//     ) {
//       console.log('[Matting/index] 重新初始化画布显示模式')
//       // 重新初始化画布，使用原始图像 + 红色蒙层模式
//       initCanvas()
//     }
//   }
// )

// 监听拖动模式变化（样式更新已移至 store 中处理）
watch(() => batchStore.maskDragMode, (newDragMode) => {
  console.log('[Matting/index] 拖动模式变化:', newDragMode)
  // 样式更新现在由 store 的 updateCanvasZoom 方法处理
})

// 监听 store 中的缩放值变化，同步到 Moveable
watch(() => batchStore.maskZoom, (newZoom) => {
  console.log('[Matting/index] Store 缩放值变化:', newZoom)

  // 如果缩放值变化不是由 Moveable 引起的，则更新 Moveable 的缩放
  if (Math.abs(manualTransform.value.scale.x - newZoom) > 0.01) {
    manualTransform.value.scale = { x: newZoom, y: newZoom }
    console.log('[Matting/index] 同步缩放到 Moveable:', newZoom)

    // 如果是重置操作（缩放为1），同时重置位置
    if (newZoom === 1) {
      manualTransform.value.translate = { x: 0, y: 0 }
      console.log('[Matting/index] 重置操作，同时重置位置')
    }

    // 更新 Moveable 组件
    if (manualMoveableRef.value) {
      nextTick(() => {
        manualMoveableRef.value.updateRect()
      })
    }
  }
})

// ================ 手动精修相关函数 ================

onMounted(() => {
  initProcessor()

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)

  // 添加文档点击事件监听，用于取消Moveable选择
  document.addEventListener('click', handleDocumentClick)

  // 监听手动精修更新事件
  window.addEventListener('mattingImageUpdated', handleMattingImageUpdated)

  // 缓存matting-image元素引用
  mattingImageElement.value = document.querySelector('.matting-image')

  // 初始获取背景盒子尺寸，切换页面再回来也需要
  nextTick(() => getBackgroundBoxSize())
  // observeBackgroundBox() // 初始化并启动观察者 // 是否必要？抠图后切换页面回来陷入死循环
})

onUnmounted(() => {
  batchStore.unBindEvent()

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 移除文档点击事件监听
  document.removeEventListener('click', handleDocumentClick)

  // 移除手动精修更新事件监听
  window.removeEventListener('mattingImageUpdated', handleMattingImageUpdated)

  // // 断开 MutationObserver 连接
  // if (backgroundBoxObserver) {
  //   backgroundBoxObserver.disconnect()
  //   backgroundBoxObserver = null
  // }

  if (MattingProcessor.worker) {
    MattingProcessor.stop()
  }
  resetState()
})

//================================= matting ===================================
// 添加文件列表状态
const fileList = ref<any[]>([])
// 状态变量
const imageState = ref('NoImage')
const file = ref<File | null>(null)
const srcImageUrl = ref('')
const processedImageUrl = ref('')
const progressValue = ref(0)
const progressText = ref('初始化中...')
const workerInit = ref(false)
const isParamReady = ref(false)
const showProgress = computed(() => {
  return (
    progressValue.value > 0 &&
    progressValue.value < 100 &&
    progressText.value !== '抠图处理完成'
  )
})

// 错误处理
const handleError = (error: unknown) => {
  console.error('Error:', error)
  imageState.value = 'NoImage'

  ElMessage({
    type: 'error',
    message:
      error instanceof Error
        ? error.message
        : String(error) || 'An error occurred',
    duration: 5000,
  })
}

// 处理图像抠图
const processImage = (imageUrl: string) => {
  try {
    if (!imageUrl) {
      console.error('No image URL provided')
      return
    }

    // 更新状态
    imageState.value = 'Processing'
    progressValue.value = 0
    progressText.value = '准备抠图处理...'

    // 检查处理器是否已初始化
    if (!MattingProcessor.worker) {
      console.log('Worker not initialized, initializing now...')
      initProcessor()
        .then(() => {
          // 初始化完成后继续处理
          continueProcessing()
        })
        .catch((error) => {
          handleError(
            'Failed to initialize processor: ' +
              (error instanceof Error ? error.message : String(error))
          )
        })
    } else {
      // 处理器已初始化，直接处理
      continueProcessing()
    }

    function continueProcessing() {
      // 如果当前有选中的图片，则将抠图结果保存到当前图片
      if (currentImg.value) {
        // 设置默认背景色
        if (!currentImg.value.backgroundColor) {
          currentImg.value.backgroundColor = 'transparent'
        }

        // 使用MattingProcessor处理图像
        MattingProcessor.predictURL(imageUrl, { file: file.value })
      } else {
        // 如果没有当前图片，只处理抠图不保存到 store
        MattingProcessor.predictURL(imageUrl, { file: file.value })
      }
    }
  } catch (error) {
    console.error('Error in processImage:', error)
    handleError(
      'Error processing image: ' +
        (error instanceof Error ? error.message : String(error))
    )
  }
}

// 初始化处理器
const initProcessor = () => {
  console.log('Initializing processor...')

  return new Promise<void>((resolve, reject) => {
    // 设置回调
    MattingProcessor.onCompleted = (result: {
      url: string
      width: number
      height: number
      userData?: any
    }) => {
      // console.log('Processing completed:', result)
      processedImageUrl.value = result.url
      imageState.value = 'Processed'

      // 设置进度为100%，显示完成状态
      progressValue.value = 100
      progressText.value = '抠图处理完成'

      // 延迟一下再隐藏进度条，让用户看到100%
      // setTimeout(() => progressValue.value = 0, 500)

      // 将抠图结果保存到 store 中
      if (currentImg.value) {
        // 设置抠图后的图片
        currentImg.value.mattingImage = result.url

        // 设置默认背景色
        if (!currentImg.value.backgroundColor) {
          currentImg.value.backgroundColor = 'transparent'
        }

        // 生成抠图后的图片与背景的组合图
        createMattingCropImage()
      }
    }

    MattingProcessor.onProgress = (val: number, maxVal: number) => {
      if (maxVal) {
        const percent = Math.round((val / maxVal) * 100)
        // console.log(`Processing progress: ${percent}%`)

        // 更新进度条
        progressValue.value = percent
        progressText.value = `抠图处理中 ${percent}%`

        // 当进度完成时
        if (percent >= 100) {
          progressText.value = `抠图处理完成`
          // 延迟一下再隐藏进度条，让用户看到100%
          // setTimeout(() => progressValue.value = 0, 500)
        }
      } else {
        progressValue.value = 1 // 设置为1而不是0，确保进度条显示
        progressText.value = '初始化中...'
      }
    }

    MattingProcessor.onError = (error: unknown) => {
      console.error('Processor error:', error)

      // 隐藏进度条
      progressValue.value = 0
      progressText.value = '处理失败'

      // 显示错误信息
      handleError(error)
      reject(error)
    }

    MattingProcessor.onDataFinish = () => {
      // console.log('Data initialization finished')
      // 如果有缓存的文件请求，可以在这里处理
      resolve()
    }

    // 初始化Worker
    try {
      MattingProcessor.warmup()
    } catch (error: unknown) {
      console.error('Failed to initialize processor:', error)
      handleError(
        'Failed to initialize processor: ' +
          (error instanceof Error ? error.message : String(error))
      )
      reject(error)
    }
  })
}

// 重置状态
const resetState = () => {
  console.log('Resetting state...')
  imageState.value = 'NoImage'
  file.value = null
  fileList.value = []

  // 释放图像资源
  if (srcImageUrl.value) {
    URL.revokeObjectURL(srcImageUrl.value)
    srcImageUrl.value = ''
  }
  if (processedImageUrl.value) {
    URL.revokeObjectURL(processedImageUrl.value)
    processedImageUrl.value = ''
  }

  // 重置进度
  progressValue.value = 0
  progressText.value = '初始化中...'

  // 使用更标准的方式来帮助释放内存
  // 设置大型对象为 null 来帮助垃圾回收器识别可回收对象
  // 注意：垃圾回收仍由浏览器自动管理，我们只能提示而非强制

  // 强制终止当前处理器
  if (MattingProcessor.worker) {
    console.log('Stopping current worker...')
    try {
      MattingProcessor.stop()
      MattingProcessor.worker = null
    } catch (error) {
      console.error('Error stopping worker:', error)
    }
  }
}

// 定义类型接口
interface MattingProcessorType {
  worker: Worker | null
  workerPath: string
  isBusy: boolean
  params: ArrayBuffer | null
  waitingPromise: {
    resolve: ((value: any) => void) | null
    reject: ((reason?: any) => void) | null
  }
  isAutoFullSize: boolean
  initPromise?: [() => void, (error: any) => void]
  userData?: any
  onCompleted?: (result: {
    url: string
    width: number
    height: number
    userData?: any
  }) => void
  onProgress?: (val: number, maxVal: number) => void
  onError?: (error: unknown) => void
  onDataFinish?: () => void
  init(): Promise<void>
  errorHandle(msg: unknown): void
  setAutoFullSize(value: boolean): void
  getRmbgParamsDownPrecent(): number
  downRmbgParams(): Promise<void>
  testDataWarmupFinish(): void
  getImageData(image: HTMLImageElement): ImageData
  predict(image: HTMLImageElement, type?: string): void
  getIdToken(): string | null
  predictURL(
    url: string | HTMLImageElement,
    userData?: any,
    type?: string
  ): void
  _predictURL(url: string | HTMLImageElement, type?: string): void
  stop(): void
  warmup(): void
  createNewWorker(): void
  onmessage(data: any): void
}
// 图像处理类
const MattingProcessor: MattingProcessorType = {
  worker: null,
  workerPath: '/js/matting/worker.js',
  isBusy: false,
  params: null,
  waitingPromise: {
    resolve: null,
    reject: null,
  },
  isAutoFullSize: false,

  // 初始化Worker
  init() {
    console.log('Starting initialization...')
    if (this.worker) {
      console.log('Worker already exists')
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        // 创建新的Worker
        this.worker = new Worker(this.workerPath)
        this.worker.onmessage = this.onmessage.bind(this)
        this.worker.onerror = (e: ErrorEvent) => {
          console.error('Worker error:', e)
          this.errorHandle(e.message)
          reject(new Error(e.message))
        }

        // 设置初始化回调
        this.initPromise = [resolve, reject]
      } catch (error: unknown) {
        console.error('Failed to create worker:', error)
        this.errorHandle(
          'Failed to initialize worker: ' +
            (error instanceof Error ? error.message : String(error))
        )
        reject(error)
      }
    })
  },

  // 错误处理
  errorHandle(msg: unknown) {
    console.error('Error:', msg)
    if (this.onError) {
      this.onError(msg)
    }
  },

  // 设置自动全尺寸模式
  setAutoFullSize(value: boolean) {
    this.isAutoFullSize = value
  },

  // 获取参数下载进度
  getRmbgParamsDownPrecent() {
    // 在实际应用中，这里应该返回下载进度
    return isParamReady.value ? 100 : 0
  },

  // 下载抠图参数
  downRmbgParams() {
    return new Promise((resolve, reject) => {
      // 使用正确的参数文件路径
      const paramFilePath = '/js/matting/matting.tf'

      loading.value = true
      // 下载参数文件
      fetch(paramFilePath)
        .then((response) => {
          if (!response.ok) {
            throw new Error(
              `Failed to download parameters: ${response.status} ${response.statusText}`
            )
          }
          return response.arrayBuffer()
        })
        .then((buffer) => {
          // console.log(`Downloaded parameters: ${buffer.byteLength} bytes`)
          this.params = buffer
          isParamReady.value = true
          this.testDataWarmupFinish()
          resolve(void 0)
        })
        .catch((error) => {
          console.error('Parameter download error:', error)
          this.errorHandle('Fail to download module: ' + error.message)
          reject(error)
        })
        .finally(() => {
          loading.value = false
        })
    })
  },

  // 测试数据预热完成
  testDataWarmupFinish() {
    if (this.onDataFinish && isParamReady.value && workerInit.value) {
      this.onDataFinish()
    }
  },

  // 获取图像数据
  getImageData(image: HTMLImageElement) {
    const canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height
    const ctx = canvas.getContext('2d')
    if (!ctx) throw new Error('Failed to get canvas context')
    ctx.drawImage(image, 0, 0)
    return ctx.getImageData(0, 0, image.width, image.height)
  },

  // 预测图像
  predict(image: HTMLImageElement, type?: string) {
    if (!this.worker) {
      console.error('Worker not initialized')
      this.errorHandle('Worker not initialized')
      return
    }

    if (!this.params) {
      console.error('Parameters not loaded')
      this.errorHandle('Parameters not loaded')
      return
    }

    // console.log('Predicting image:', {
    //   width: image.width,
    //   height: image.height,
    //   type: type,
    //   hasParams: !!this.params,
    //   paramsSize: this.params ? this.params.byteLength : 0,
    // })

    try {
      const imageData = this.getImageData(image)
      let authorization = null

      // 检查是否需要授权
      if (
        ((type && type.includes('FullSize')) || this.isAutoFullSize) &&
        (window as any).userConfirmed &&
        (window as any).userConfirmed.value
      ) {
        authorization = this.getIdToken()
      }

      // console.log('Sending data to worker:', {
      //   cmd: 'predict',
      //   width: image.width,
      //   height: image.height,
      //   dataSize: imageData.data.buffer.byteLength,
      //   paramsSize: this.params.byteLength,
      //   hasAuthorization: !!authorization,
      // })

      // 发送消息给Worker
      this.worker.postMessage(
        {
          cmd: 'predict',
          width: image.width,
          height: image.height,
          data: imageData.data.buffer,
          params: this.params,
          Authorization: authorization,
        },
        [imageData.data.buffer]
      )

      // 更新进度
      if (this.onProgress) {
        this.onProgress(0, 1)
      }
    } catch (error: unknown) {
      console.error('Error in predict method:', error)
      this.errorHandle(
        'Failed to process image: ' +
          (error instanceof Error ? error.message : String(error))
      )
    }
  },

  // 获取ID令牌
  getIdToken() {
    // 在实际应用中，这里应该返回用户的ID令牌
    return null
  },

  // 预测URL
  predictURL(url: string | HTMLImageElement, userData?: any, type?: string) {
    this.userData = userData
    this.init().then(() => {
      if (typeof url === 'string') {
        this._predictURL(url, type)
      } else {
        this.predict(url, type)
      }
    })
  },

  // 预测URL（内部方法）
  _predictURL(url: string | HTMLImageElement, type?: string) {
    // console.log('Loading image from URL for prediction:', {
    //   url:
    //     typeof url === 'string'
    //       ? url.length > 50
    //         ? url.substring(0, 50) + '...'
    //         : url
    //       : 'Image object',
    //   type,
    // })

    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      // console.log('Image loaded from URL successfully:', {
      //   width: img.width,
      //   height: img.height,
      // })
      this.predict(img, type)
    }

    img.onerror = (error) => {
      console.error('Failed to load image from URL:', error)
      this.errorHandle(
        'Failed to load image: ' +
          (typeof url === 'string' ? url : 'provided image')
      )
    }

    if (typeof url === 'string') {
      img.src = url
    } else {
      // 如果传入的是一个已经加载的Image对象
      this.predict(url, type)
    }
  },

  // 停止处理
  stop() {
    console.log('Stopping worker and cleaning up resources...')
    if (this.worker) {
      try {
        // 终止worker
        this.worker.terminate()
        this.worker = null
      } catch (error) {
        console.error('Error terminating worker:', error)
      }

      // 重置状态
      this.isBusy = false
      this.waitingPromise.resolve = null
      this.waitingPromise.reject = null
      this.userData = null

      // 重置参数以释放内存
      // 注意：这里我们释放参数以确保内存被正确释放
      this.params = null
      isParamReady.value = false

      workerInit.value = false
    }
  },

  // 预热
  warmup() {
    console.log('Starting warmup...')

    // 如果已经有worker在运行，先停止
    if (this.worker) {
      console.log('Worker already exists, stopping it first...')
      try {
        this.worker.terminate()
        this.worker = null
        // 等待一小段时间确保资源已释放
        setTimeout(() => this.createNewWorker(), 100)
        return
      } catch (error) {
        console.error('Error terminating existing worker:', error)
      }
    }

    this.createNewWorker()
  },

  // 创建新的Worker
  createNewWorker() {
    // 创建Worker
    try {
      console.log('Creating new worker...')
      this.worker = new Worker(this.workerPath)

      if (this.worker) {
        this.worker.onmessage = (e: MessageEvent) => {
          this.onmessage(e.data)
        }
        this.worker.onerror = (e: ErrorEvent) => {
          console.error('Worker error during warmup:', e)
          this.errorHandle(e.message)
        }

        // 检查参数是否已经加载
        if (this.params && isParamReady.value) {
          console.log('Parameters already loaded, skipping download')
          // 参数已经加载，不需要重新下载
          setTimeout(() => {
            this.testDataWarmupFinish()
          }, 100)
        } else {
          // 需要下载参数
          console.log('Downloading parameters...')
          this.downRmbgParams()
            .then(() => {
              // console.log('Parameters downloaded successfully during warmup')
            })
            .catch((error: unknown) => {
              console.error(
                'Failed to download parameters during warmup:',
                error
              )
            })
        }
      } else {
        this.errorHandle('Failed to create worker instance')
      }
    } catch (error: unknown) {
      console.error('Error during warmup:', error)
      this.errorHandle(
        'Failed to initialize module: ' +
          (error instanceof Error ? error.message : String(error))
      )
    }
  },

  // 处理Worker消息
  onmessage(data: any) {
    const { cmd } = data
    // console.log('Worker message received:', cmd, data)

    switch (cmd) {
      case 'Init':
        // console.log(
        //   'Worker initialized with capabilities:',
        //   data.useSimd ? 'SIMD' : 'No SIMD',
        //   data.useThreads ? 'Threads' : 'No Threads'
        // )
        workerInit.value = true
        if (this.initPromise) {
          this.initPromise[0]()
          this.initPromise = undefined
        }
        this.testDataWarmupFinish()
        break

      case 'predictFinish':
        if (this.onCompleted) {
          // console.log('Processing finished. Data received:', {
          //   width: data.width,
          //   height: data.height,
          //   dataSize: data.data ? data.data.byteLength : 'unknown',
          //   useSimd: data.useSimd,
          //   useThreads: data.useThreads,
          // })

          try {
            // 创建一个canvas来处理图像数据
            const canvas = document.createElement('canvas')
            canvas.width = data.width
            canvas.height = data.height
            const ctx = canvas.getContext('2d')

            // 创建ImageData对象
            const imageData = new ImageData(
              new Uint8ClampedArray(data.data),
              data.width,
              data.height
            )

            // 将ImageData绘制到canvas
            if (!ctx) throw new Error('Failed to get canvas context')
            ctx.putImageData(imageData, 0, 0)

            // 从canvas获取图像URL
            const url = canvas.toDataURL('image/png')
            // console.log('Image URL created successfully')

            this.onCompleted({
              url,
              width: data.width,
              height: data.height,
              userData: this.userData,
            })
          } catch (error: unknown) {
            console.error('Error creating image from data:', error)
            this.errorHandle(
              'Failed to create image: ' +
                (error instanceof Error ? error.message : String(error))
            )
          }
        }
        break

      case 'progressCB':
        if (this.onProgress) {
          this.onProgress(data.val, data.maxVal)
        }
        break

      case 'error':
        console.error('Worker reported error:', data.msg, data.code)
        this.errorHandle(data.msg || 'Unknown error')
        break

      case 'edgeEnhanceFinish':
      case 'predictHarmFinish':
        if (this.waitingPromise.resolve) {
          try {
            // 创建一个canvas来处理图像数据
            const canvas = document.createElement('canvas')
            canvas.width = data.width
            canvas.height = data.height
            const ctx = canvas.getContext('2d')

            // 创建ImageData对象
            const imageData = new ImageData(
              new Uint8ClampedArray(data.data),
              data.width,
              data.height
            )

            // 将ImageData绘制到canvas
            if (!ctx) throw new Error('Failed to get canvas context')
            ctx.putImageData(imageData, 0, 0)

            // 从canvas获取图像URL
            const url = canvas.toDataURL('image/png')

            this.waitingPromise.resolve({
              url,
              width: data.width,
              height: data.height,
            })
          } catch (error) {
            console.error('Error creating image from data:', error)
            if (this.waitingPromise.reject) {
              this.waitingPromise.reject(error)
            }
          } finally {
            this.waitingPromise.resolve = null
            this.waitingPromise.reject = null
          }
        }
        break

      default:
        console.log('Unhandled worker message:', cmd, data)
        break
    }
  },
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
